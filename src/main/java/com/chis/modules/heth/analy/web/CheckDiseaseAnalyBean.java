package com.chis.modules.heth.analy.web;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.apache.commons.collections.map.LinkedMap;
import org.primefaces.context.RequestContext;
import org.primefaces.model.CheckboxTreeNode;
import org.primefaces.model.TreeNode;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.analy.pojo.MyData;
import com.chis.modules.heth.analy.service.HethAnalyStaQueryServiceImpl;
import com.chis.modules.heth.analy.utils.EchartsUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.github.abel533.echarts.axis.AxisLabel;
import com.github.abel533.echarts.axis.CategoryAxis;
import com.github.abel533.echarts.axis.ValueAxis;
import com.github.abel533.echarts.code.AxisType;
import com.github.abel533.echarts.code.Magic;
import com.github.abel533.echarts.code.Orient;
import com.github.abel533.echarts.code.Tool;
import com.github.abel533.echarts.code.Trigger;
import com.github.abel533.echarts.code.X;
import com.github.abel533.echarts.code.Y;
import com.github.abel533.echarts.feature.MagicType;
import com.github.abel533.echarts.json.GsonOption;
import com.github.abel533.echarts.series.Bar;
import com.github.abel533.echarts.style.ItemStyle;
import com.github.abel533.echarts.style.itemstyle.Normal;
import com.google.common.collect.Lists;


/***
 *  <p>类描述：职业健康检测疾病分析</p>
 *
 * @ClassAuthor maox,2019年12月3日,CheckDiseaseAnalyBean
 * <AUTHOR>
 *
 */
@ManagedBean(name = "checkDiseaseAnalyBean")
@ViewScoped
public class CheckDiseaseAnalyBean {
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder
			.getBean(CommServiceImpl.class);
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	private HethAnalyStaQueryServiceImpl hethStaQueryServiceImpl = SpringContextHolder
			.getBean(HethAnalyStaQueryServiceImpl.class);
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap()
			.get(SessionData.SESSION_DATA);
	
	private List<TsZone> searchZoneList;
	private String searchZoneCode;
	private String searchZoneName;
	private Date searchStartDate;
	private Date searchEndDate;
	/** 分析维度 */
	private Integer dimension;
	/** 统计项 */
	private Integer analyseTerms;
	/** 在岗状态 */
	private List<TsSimpleCode> onguardList;
	
	private TreeNode sortTree;
	/** 选择的危害因素 */
	private TreeNode[] selectBigHarms;
	/** 选择的危害因素，以逗号拼接 */
	private String selectBigHarmName;
	/** 选择的危害因素id，以逗号拼接 */
	private String selectBigHarmIds;
	/** 危害因素大类 */
	private List<TsSimpleCode> badRsnsParentList;
	/** 统计项表头 */
	private List<TsSimpleCode> headList;
	/** 表格标题 */
	private String tableTitle;
	private List<Object[]> dataList;
	
	/** 柱状图所选地区标记 */
	private Integer zoneTag = 0;
	/** 柱状图 */
	private String chartJson;
	/** 饼图 */
	private String pieJson;
	/** 性别饼图 */
	private String sexPieJson;
	/** 折线图 */
	private String lineJson;
	private Map<String,String> zoneMap;
	
	public CheckDiseaseAnalyBean(){
		this.setDimension(1);// 默认用人单位地区
		this.analyseTerms = 1;// 默认在岗状态
		this.searchStartDate = DateUtils.getYearFirstDay(new Date());
		this.searchEndDate = DateUtils.getDateOnly(new Date());
		initZone();
		initSortTree();
		onguardList = this.commService.findLevelSimpleCodesByTypeId("5009");
		initHeadList();
		zoneMap = hethStaQueryServiceImpl.getZoneMap();
	}
	
	
	/**
	 * <p>
	 * 方法描述：初始化地区
	 * </p>
	 * 
	 * @MethodAuthor maox
	 * */
	public void initZone() {
		TsZone tsZone = this.sessionData.getUser().getTsUnit().getFkByManagedZoneId();
		if (null==tsZone) {
			tsZone = this.sessionData.getUser().getTsUnit().getTsZone();
		}
		this.searchZoneCode = tsZone.getZoneCode();
		this.searchZoneName = tsZone.getZoneName();
		this.searchZoneList = this.systemModuleService.findZoneListICanSee(
				false, tsZone.getZoneCode());
	}
	
	
	/***
	 *  <p>方法描述：柱状图</p>
     *
     * @MethodAuthor maox,2019年12月5日,buildChartJson
	 * @param title
	 * @param xAxisTitle
	 * @param yAxisTitle
	 * @param obj
	 * @return
	 */
	public void buildChartJson() {
		String xName = "";
		String title = this.searchZoneName
				+ (null != searchStartDate ? DateUtils
						.getChineseStringDate(this.searchStartDate) : "")
				+ "至"
				+ (null != searchEndDate ? DateUtils
						.getChineseStringDate(this.searchEndDate) : "")+"职业健康检查疾病分析";
		if(1==dimension){
			title+="（"+"用人单位地区"+"）";
			xName = "地区";
		}else if(2==dimension){
			title+="（"+"检查机构"+"）";
			xName = "检查机构";
		}else if(3==dimension){
			title+="（"+"行业类别"+"）";
			xName = "行业类别";
		}else if(4==dimension){
			title+="（"+"经济类型"+"）";
			xName = "经济类型";
		}else if(5==dimension){
			title+="（"+"企业规模"+"）";
			xName = "企业规模";
		}
		
		Object[] xdata = new Object[dataList.size()-1];// 柱状图x轴数据
		List<Bar> bars = Lists.newArrayList();
		Bar bar = new Bar();
		bar.setName("总数");
		bar.setStack("bar");
		bar.setBarWidth(50);
		ItemStyle it = new ItemStyle();
		Normal normal = new Normal();
		normal.barBorderRadius(10);
		it.setNormal(normal);
		bar.setItemStyle(it);
		List<String> list = new ArrayList<>();
		if (null != dataList && dataList.size() > 0) {
			for(int i = 0; i < dataList.size()-1; i++){
				Object[] obj = dataList.get(i);
				if(2== dimension){
					xdata[i]=obj[1];
				}else{
					xdata[i]=obj[0];
				}
				
				
				list.add(obj[3].toString());
				
			}
		}
		bar.setData(list);
		bars.add(bar);
		
		String initBarXml = initBarXml(title, xName,
				"总数",null,X.left, xdata, bars, 2== dimension);
		chartJson = initBarXml;
	}
	
	private  String initBarXml(String title, String xAxisTitle,
			String yAxisTitle,Object[] legend,X xlegend, Object[] xdate, List<Bar> bars,
			boolean ifdatazoom) {
		GsonOption op = new GsonOption();
		op.tooltip().trigger(Trigger.axis);
		// 定义工具
		op.toolbox()
				.show(true)
				.feature(new MagicType(Magic.line, Magic.bar), Tool.restore,
						Tool.saveAsImage).padding(20).x(X.right).y(Y.top)
				.orient(Orient.horizontal)
				.padding(new Integer[]{30,30,0,0});

		Object[] titles = new Object[1];
		titles[0] = "总数";
		
		if (null!=legend) {
			op.legend().data(legend);
		}
		op.legend().y(Y.top);
		op.legend().x(xlegend);
		op.legend().padding(new Integer[]{20,0,0,0});
		op.grid().y(65);
		op.grid().x(85);
		op.grid().x2(65);
		op.grid().y2("30%");
		op.title().x(X.center);
		op.title(title);
		op.title().textStyle().fontFamily("微软雅黑");
		op.title().textStyle().fontWeight("bolder");
		op.title().textStyle().setColor("rgba(30,144,255,0.8)");

		if (ifdatazoom) {
			op.dataZoom().show(true);
			op.dataZoom().realtime(true);
			op.dataZoom().start(0);
			Bar bar = bars.get(0);
			List datas = bar.getData();
			if (null != datas && datas.size() <= 10) {
				op.dataZoom().end(100);
			} else if (datas.size() > 10) {
				BigDecimal one = new BigDecimal(10);
				BigDecimal two = new BigDecimal(datas.size());
				BigDecimal divide = one.multiply(new BigDecimal(100)).divide(
						two, 0);
				op.dataZoom().end(divide.intValue());
			}
		}
		// 横坐标
		CategoryAxis xAxis = new CategoryAxis();
		xAxis.setData(Arrays.asList(xdate));
		xAxis.setType(AxisType.category);
		xAxis.setShow(true);
		xAxis.setName(xAxisTitle);
		AxisLabel xAxisLabel = new AxisLabel();
		xAxisLabel
				.setFormatter("function(value){return (value.length >12 ? (value.slice(0,12)+'\n'+value.slice(12,value.length)) : value )}");
		xAxisLabel.textStyle().color("#000000");
		xAxisLabel.setInterval(0);
		xAxisLabel.margin(15);
		if(4 ==dimension || 5 ==dimension){
			xAxisLabel.setRotate(0);
		}else{
			xAxisLabel.setRotate(20);
		}
		
		xAxis.setAxisLabel(xAxisLabel);
		xAxis.splitLine().show(true);
		xAxis.setBoundaryGap(true);
		op.xAxis().add(xAxis);

		// 纵坐标
		ValueAxis valueAxis = new ValueAxis();
		valueAxis.setType(AxisType.value);
		valueAxis.setName(yAxisTitle);
		valueAxis.axisTick().show(false);
		valueAxis.axisLabel().textStyle().color("#000000");
		Double [] a = {0.0,0.1};
		valueAxis.setBoundaryGap(a);
		/*valueAxis.axisLabel().formatter("{value}");*/
		op.yAxis().add(valueAxis);
			
		op.series().addAll(bars);
		return op.toString();
	}
	
	/***
	 *  <p>方法描述：性别比例</p>
     *
     * @MethodAuthor maox,2019年12月5日,buildSexPieJson
	 * @param val
	 */
	public void buildSexPieJson(Object[] val) {
		this.sexPieJson = null;
		if (null == val) {
			return;
		}
		Object[] legend = new Object[2];
		legend[0] = "男性";
		legend[1] = "女性";
		List<MyData> data = new ArrayList<MyData>();
		if (null == val[4]) {
			val[4] = 0;
		}
		data.add(EchartsUtils.initMyData("男性",
				StringUtils.objectToString(val[4]), null));
		if (null == val[5]) {
			val[5] = 0;
		}
		data.add(EchartsUtils.initMyData("女性",
				StringUtils.objectToString(val[5]), null));
		String title = "职业健康检查疾病性别分布（"+(2 == dimension ? val[1] : val[0]) + "）";
		this.sexPieJson = EchartsUtils.initPieXml(data, title, legend);
	}
	
	/***
	 *  <p>方法描述：统计项</p>
     *
     * @MethodAuthor maox,2019年12月5日,buildPieJson
	 * @param val
	 */
	public void buildPieJson(Object[] val) {
		this.pieJson = null;
		if (null == val) {
			return;
		}
		List<MyData> data = new ArrayList<MyData>();
		int i = 10;
		Object[] legend = new Object[headList.size()];
		for (int j = 0; j < this.headList.size(); j++) {
			TsSimpleCode t = this.headList.get(j);
			String name = t.getCodeName().length() > 6 ? t.getCodeName()
					.substring(0, 6) + "..." : t.getCodeName();
			if (null == val[i]) {
				val[i] = 0;
			}
			data.add(EchartsUtils.initMyData(name,
					StringUtils.objectToString(val[i]), null));
			legend[j] = name;
			i++;
		}
		String title = "职业健康检查疾病"+ (1 == analyseTerms ? "在岗状态" :  "危害因素")+"分布（" +(2 == dimension ? val[1] : val[0])+ "）";
		this.pieJson = EchartsUtils.initPieXml(data, title, legend);
	}
	
	/***
	 *  <p>方法描述：折线</p>
     *
     * @MethodAuthor maox,2019年12月5日,buildLineJson
	 * @param val
	 */
	public void buildLineJson(Object[] val) {
		this.lineJson = null;
		if (null == val) {
			return;
		}
		int eyear = DateUtils.getYear(searchEndDate);
		String zoneGb = null;
		String orgName = "";
		String codeLevelCode = null;
		
		if (1 == dimension) {
			zoneGb = StringUtils.objectToString(val[1]);
		} else if (2 == dimension){
			if (null != val[1]) {
				orgName = val[1].toString();
			}
		}else{
			if (null != val[1]) {
				codeLevelCode = StringUtils.objectToString(val[1]);
			}
		}
		if(StringUtils.isBlank(zoneGb)){
			zoneGb = searchZoneCode;
		}
		List<Object[]> list = hethStaQueryServiceImpl.getDuringYearDiseaseDatas(
				dimension, zoneGb, orgName, eyear - 4, eyear,
				this.selectBigHarmIds,codeLevelCode);
		Map<String, Object[]> map = new HashMap<>();
		if (null != list && list.size() > 0) {
			for (Object[] obj : list) {
				map.put(StringUtils.objectToString(obj[1]), obj);
			}
		}
		List<Object[]> results = new ArrayList<>();
		Integer[] xdata = new Integer[5];
		Object[] totaldata = new Object[5];
		for (int i = 0; i < 5; i++) {
			Integer year = (eyear - 4) + i;
			xdata[i] = year;
			if (null == map.get(year.toString())) {
				totaldata[i] = 0;
			} else {
				Object[] obj = map.get(year.toString());
				totaldata[i] = obj[0];
			}
		}
		results.add(totaldata);
		Object[] legend = new Object[] { "总数" };
		String title = "职业健康检查疾病同期对比趋势分析（"+(2 == dimension ? val[1] : val[0]) + "）";
		this.lineJson = EchartsUtils.initLineXml(results, legend, title, xdata,"总数",X.right);

	}
	
	public void changeZoneBar() {
		try {
			if (null != this.dataList && this.dataList.size() > 0) {
				Object[] objArray = null;
				if (null == zoneTag) {
					objArray = this.dataList.get(0);// 默认第一个地区
				} else {
					objArray = this.dataList.get(zoneTag);
				}
				buildSexPieJson(objArray);
				buildPieJson(objArray);
				buildLineJson(objArray);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/***
	 *  <p>方法描述：页面查询</p>
     *
     * @MethodAuthor maox,2019年12月3日,searchAction
	 */
	public void searchAction() {
		boolean flag = false;
		if (null == this.searchStartDate) {
			JsfUtil.addErrorMessage("检查周期开始日期不能为空！");
			flag = true;
		}
		if (null == this.searchEndDate) {
			JsfUtil.addErrorMessage("检查周期结束日期不能为空！");
			flag = true;
		}
		if (null != this.searchStartDate && null != this.searchEndDate) {
			if (this.searchEndDate.before(this.searchStartDate)) {
				JsfUtil.addErrorMessage("检查周期开始日期应小于等于结束日期！");
				flag = true;
			}
		}
		if (selectBigHarms != null && selectBigHarms.length > 10) {
			int i = 0;
			for (TreeNode treeNode : selectBigHarms) {
				TsSimpleCode simpleCode = (TsSimpleCode) treeNode.getData();
				if (simpleCode.getCodeLevelNo().equals(simpleCode.getCodeNo())) {
					continue;
				}
				i++;
			}
			if (i > 10) {
				JsfUtil.addErrorMessage("危害因素统计项不能超过十个！");
				flag = true;
			}
		}
		if (flag) {
			return;
		}
		initHeadList();
		this.chartJson = null;
		this.pieJson = null;
		this.sexPieJson = null;
		this.lineJson = null;
		
		this.dataList = new ArrayList<>();
		List<Object[]> list = hethStaQueryServiceImpl.getcheckAnalyseDatas(
				this.dimension, this.analyseTerms, headList,
				this.searchZoneCode, this.searchStartDate, this.searchEndDate,
				this.selectBigHarmIds);
		if(null != list && list.size() > 0){
			Map<String, Integer> dataMap = new HashMap<>();
			// 合计
			Object[] hjObj = new Object[this.headList.size() + 14];
			Double badMax= 0.0;
			Double badMin=100.0;
			Integer totalPsnNums = 0;
			Integer totalworkPsnNums = 0;
			for(Object[] orr :list){
				totalPsnNums += null==orr[this.headList.size() + 12]?0:Integer.parseInt(orr[this.headList.size() + 12].toString());
				totalworkPsnNums += null==orr[this.headList.size() + 13]?0:Integer.parseInt(orr[this.headList.size() + 13].toString());
				if(2==dimension){
					orr[0] = zoneMap.get(orr[0].toString());
					if(dataMap.containsKey(orr[0].toString())){
						Integer a = dataMap.get(orr[0].toString());
						dataMap.put(orr[0].toString(), a+1);
					}else{
						dataMap.put(orr[0].toString(), 1);
					}
				}
				
				//疑似总人数
				orr[3] = null==orr[3]?"0":orr[3].toString();
				int a =Integer.parseInt(orr[3].toString());
				hjObj[3] =null==hjObj[3]?a:(Integer.parseInt(hjObj[3].toString())+a);
				//男
				orr[4] = null==orr[4]?"0":orr[4].toString();
				int b =Integer.parseInt(orr[4].toString());
				hjObj[4] =null==hjObj[4]?b:(Integer.parseInt(hjObj[4].toString())+b);
				//女
				orr[5] = null==orr[5]?"0":orr[5].toString();
				int c =Integer.parseInt(orr[5].toString());
				hjObj[5] =null==hjObj[5]?c:(Integer.parseInt(hjObj[5].toString())+c);
				
				if(orr[6] != null && !"-1".equals(orr[6].toString())){
					if(orr[6] != null && Double.parseDouble(orr[6].toString())>badMax){
						badMax = Double.parseDouble(orr[6].toString());
					}
					orr[6] = null==orr[6]?"——":transformYear(orr[6].toString());
				}else{
					orr[6] = "——";
				}
				
				
				if(orr[7] != null && !"-1".equals(orr[7].toString())){
					if(orr[7] != null && Double.parseDouble(orr[7].toString())<badMin){
						badMin = Double.parseDouble(orr[7].toString());
					}
					orr[7] = null==orr[7]?"——":transformYear(orr[7].toString());
				}else{
					orr[7] ="——";
				}
				
				if(orr[8] != null && !"-1".equals(orr[8].toString())){
					orr[8] = null==orr[8]?"——":transformYear(orr[8].toString());
				}else{
					orr[8] ="——";
				}
				
				if(orr[9] != null && !"-1".equals(orr[9].toString())){
					orr[9] = null==orr[9]?"——":transformYear(orr[9].toString());
				}else{
					orr[9] ="——";
				}
				
				
				
				for(int i=0;i<headList.size();i++){
					orr[10+i] = null==orr[10+i]?"0":orr[10+i].toString();
					
					int j =Integer.parseInt(orr[10+i].toString());
					hjObj[10+i] =null==hjObj[10+i]?j:(Integer.parseInt(hjObj[10+i].toString())+j);
				}
				orr[10+headList.size()] = null==orr[10+headList.size()]?"0":orr[10+headList.size()].toString();
				double m =Double.parseDouble(orr[10+headList.size()].toString());
				hjObj[10+headList.size()] = null==hjObj[10+headList.size()]?m:(Double.parseDouble(hjObj[10+headList.size()].toString())+m);
				
				orr[11+headList.size()] = null==orr[11+headList.size()]?"0":orr[11+headList.size()].toString();
				double n =Double.parseDouble(orr[11+headList.size()].toString());
				hjObj[11+headList.size()] = null==hjObj[11+headList.size()]?n:(Double.parseDouble(hjObj[11+headList.size()].toString())+n);
			}
			
			//处理合计
			//最大接害工龄
			if(badMax > 0.1){
				hjObj[6] = transformYear(badMax.toString());
			}else{
				hjObj[6] ="0月";
			}
			
			//最小接害工龄
			if(badMin > (double)0.0001 && !badMin.toString().equals("100.0")){
				hjObj[7] = transformYear(badMin.toString());
			}else{
				hjObj[7] ="0月";
			}

			//平均接害工龄
			if(hjObj[10+headList.size()] != null && Double.parseDouble(hjObj[10+headList.size()].toString()) >0.1 && hjObj[3] != null){
				Double avgBad = Double.parseDouble(hjObj[10+headList.size()].toString())/(double)totalPsnNums;
				hjObj[8] = transformYear(avgBad.toString());
			}else{
				hjObj[8] ="0月";
			}
			
			//平均工龄
			if(hjObj[11+headList.size()] != null && Double.parseDouble(hjObj[11+headList.size()].toString()) >0.1 && hjObj[3] != null){
				Double avgwork = Double.parseDouble(hjObj[11+headList.size()].toString())/(double)totalworkPsnNums;
				hjObj[9] = transformYear(avgwork.toString());
			}else{
				hjObj[9] ="0月";
			}
			hjObj[0] = "合计";
			list.add(hjObj);
			for(Object[] orr :list){
				if(2 == dimension){
					orr[11+headList.size()] = dataMap.get(orr[0].toString());
					dataMap.put(orr[0].toString(), 0);
				}else{
					orr[11+headList.size()] = 1;
				}

			}
			
		}
		
		this.dataList.addAll(list);
		
		if(dataList.size()>1){
			buildChartJson();
			if(1==dimension){
				Object [] a = dataList.get(dataList.size()-1);
				a[0] = searchZoneName;
				buildSexPieJson(a);
				buildPieJson(a);
				buildLineJson(a);
			}else{
				buildSexPieJson(dataList.get(0));
				buildPieJson(dataList.get(0));
				buildLineJson(dataList.get(0));
			}
			
		}else{
			this.chartJson = null;
			this.pieJson = null;
			this.sexPieJson = null;
			this.lineJson = null;
		}
		
		RequestContext context = RequestContext.getCurrentInstance();
		
		context.execute("buildChart()");
		context.update("mainForm");
	}
	
	/***
	 *  <p>方法描述：转变年份</p>
     *
     * @MethodAuthor maox,2019年12月4日,transformYear
	 * @param year
	 * @return
	 */
	private String transformYear(String year){
		String time = "";
		if(StringUtils.isBlank(year)){
			return null;
		}
		if("0".equals(year)){
			time = "0月";
		}else{
			String [] result  = year.split("\\.");
			if(result.length ==1){
				time = result[0]+"年";
			}else{
				double a =Double.parseDouble("0."+result[1]);
				long b = Math.round(a*(double)12);
				if("0".equals(result[0])){
					time  =  b+"月";
				}else{
					time  =  result[0]+"年"+ b+"月";
				}
				
			}
		}
		
		return time; 
	}
	
	
	
	/**
	 * 初始化危害树
	 */
	private void initSortTree() {
		badRsnsParentList = new ArrayList<>();
		this.sortTree = new CheckboxTreeNode("root", null);
		List<TsSimpleCode> list = null;
		list = commService.findScAllOrNoByTypeId("5007", true);
		if (null != list && list.size() > 0) {
			// 只有第一层
			Set<String> firstLevelNoSet = new LinkedHashSet<>();
			// 没有第一层
			Set<String> levelNoSet = new LinkedHashSet<>();
			// 所有类别
			Map<String, TsSimpleCode> menuMap = new HashMap<>();

			for (TsSimpleCode t : list) {
				menuMap.put(t.getCodeLevelNo(), t);
				/**
				 * 若层级编码不为空，并且不包含“.”,放在第一层 firstLevelNoSet存储所有层级编码
				 */
				if (StringUtils.isNotBlank(t.getCodeLevelNo())) {
					if (StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
						firstLevelNoSet.add(t.getCodeLevelNo());
						badRsnsParentList.add(t);
					} else {
						/**
						 * 层级编码存在“.”
						 */
						levelNoSet.add(t.getCodeLevelNo());
					}
				}
			}
			/**
			 *
			 */
			for (String ln : firstLevelNoSet) {
				TsSimpleCode t = menuMap.get(ln);
				TreeNode node = new CheckboxTreeNode(t, this.sortTree);
				this.addChildNode(ln, levelNoSet, menuMap, node);
			}

			menuMap.clear();
		}

	}

	private void addChildNode(String levelNo, Set<String> levelNoSet,
			Map allMap, TreeNode parentNode) {
		int level = StringUtils.countMatches(levelNo, ".");
		for (String ln : levelNoSet) {
			if (StringUtils.countMatches(ln, ".") == (level + 1)
					&& StringUtils.startsWith(ln, levelNo + ".")) {
				TreeNode node = new CheckboxTreeNode(allMap.get(ln), parentNode);
				this.addChildNode(ln, levelNoSet, allMap, node);
			}
		}
	}
	
	
	
	public void clearSelectBadRsns() {
		this.selectBigHarmName = null;
		this.selectBigHarmIds = null;
		this.selectBigHarms = null;
		List<TreeNode> children = this.sortTree.getChildren();
		if (null!=children && children.size()>0) {
			for (TreeNode node : children) {
				node.setSelected(false);
				List<TreeNode> children2 = node.getChildren();
				if (null!=children2 && children2.size()>0) {
					for (TreeNode node2 : children2) {
						node2.setSelected(false);
					}
				}
			}
		}
	}
	
	/***
	 *  <p>方法描述：</p>
     *
     * @MethodAuthor maox,2019年12月4日,hideBigHarmAction
	 */
	public void hideBigHarmAction() {
		// 遍历选择的危害因素，获取选择的id与名称
		this.selectBigHarmName = null;
		this.selectBigHarmIds = null;
		if (this.selectBigHarms != null && selectBigHarms.length > 0) {
			StringBuilder nameSb = new StringBuilder(); // 分类名称
			StringBuilder idSb = new StringBuilder(); // id
			int i = 0;
			for (TreeNode treeNode : selectBigHarms) {
				TsSimpleCode simpleCode = (TsSimpleCode) treeNode.getData();
				if (simpleCode.getCodeLevelNo().equals(simpleCode.getCodeNo())) {
					continue;
				}
				nameSb.append("，").append(
						String.valueOf(simpleCode.getCodeName()));
				idSb.append(",").append(
						String.valueOf(simpleCode.getRid()));
				i++;
			}
			if (i > 10) {
				JsfUtil.addErrorMessage("危害因素统计项不能超过十个！");
			}
			if (nameSb.toString().length() > 0) {
				this.selectBigHarmName = nameSb.toString().substring(1);
			}
			if (idSb.toString().length() > 0) {
				this.selectBigHarmIds = idSb.toString().substring(1);
			}
		}
	}
	
	private void initHeadList() {
		this.headList = new ArrayList<>();
		if (analyseTerms == 1) {
			if (null != onguardList && onguardList.size() > 0) {
				this.headList.addAll(onguardList);
			}
		} else if (analyseTerms == 2) {
			if (this.selectBigHarms != null && selectBigHarms.length > 0) {
				for (TreeNode treeNode : selectBigHarms) {
					TsSimpleCode simpleCode = (TsSimpleCode) treeNode.getData();
					if (simpleCode.getCodeLevelNo().equals(
							simpleCode.getCodeNo())) {
						continue;
					}
					this.headList.add(simpleCode);
				}
			}else {
				if (null != badRsnsParentList && badRsnsParentList.size() > 0) {
					this.headList.addAll(badRsnsParentList);
				}
			}
		} 
	}
	
	public List<TsZone> getSearchZoneList() {
		return searchZoneList;
	}
	public void setSearchZoneList(List<TsZone> searchZoneList) {
		this.searchZoneList = searchZoneList;
	}
	public String getSearchZoneCode() {
		return searchZoneCode;
	}
	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}
	public String getSearchZoneName() {
		return searchZoneName;
	}
	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}
	public Date getSearchStartDate() {
		return searchStartDate;
	}
	public void setSearchStartDate(Date searchStartDate) {
		this.searchStartDate = searchStartDate;
	}
	public Date getSearchEndDate() {
		return searchEndDate;
	}
	public void setSearchEndDate(Date searchEndDate) {
		this.searchEndDate = searchEndDate;
	}
	
	public Integer getAnalyseTerms() {
		return analyseTerms;
	}
	public void setAnalyseTerms(Integer analyseTerms) {
		this.analyseTerms = analyseTerms;
	}
	public TreeNode getSortTree() {
		return sortTree;
	}
	public void setSortTree(TreeNode sortTree) {
		this.sortTree = sortTree;
	}
	public TreeNode[] getSelectBigHarms() {
		return selectBigHarms;
	}
	public void setSelectBigHarms(TreeNode[] selectBigHarms) {
		this.selectBigHarms = selectBigHarms;
	}
	public String getSelectBigHarmName() {
		return selectBigHarmName;
	}
	public void setSelectBigHarmName(String selectBigHarmName) {
		this.selectBigHarmName = selectBigHarmName;
	}
	public String getSelectBigHarmIds() {
		return selectBigHarmIds;
	}
	public void setSelectBigHarmIds(String selectBigHarmIds) {
		this.selectBigHarmIds = selectBigHarmIds;
	}


	public List<TsSimpleCode> getBadRsnsParentList() {
		return badRsnsParentList;
	}


	public void setBadRsnsParentList(List<TsSimpleCode> badRsnsParentList) {
		this.badRsnsParentList = badRsnsParentList;
	}


	public List<TsSimpleCode> getHeadList() {
		return headList;
	}


	public void setHeadList(List<TsSimpleCode> headList) {
		this.headList = headList;
	}


	public String getTableTitle() {
		return tableTitle;
	}


	public void setTableTitle(String tableTitle) {
		this.tableTitle = tableTitle;
	}


	public List<Object[]> getDataList() {
		return dataList;
	}


	public void setDataList(List<Object[]> dataList) {
		this.dataList = dataList;
	}


	public Integer getDimension() {
		return dimension;
	}


	public void setDimension(Integer dimension) {
		this.dimension = dimension;
	}


	public List<TsSimpleCode> getOnguardList() {
		return onguardList;
	}


	public void setOnguardList(List<TsSimpleCode> onguardList) {
		this.onguardList = onguardList;
	}


	public Integer getZoneTag() {
		return zoneTag;
	}


	public void setZoneTag(Integer zoneTag) {
		this.zoneTag = zoneTag;
	}


	public String getChartJson() {
		return chartJson;
	}


	public void setChartJson(String chartJson) {
		this.chartJson = chartJson;
	}


	public String getPieJson() {
		return pieJson;
	}


	public void setPieJson(String pieJson) {
		this.pieJson = pieJson;
	}


	public String getSexPieJson() {
		return sexPieJson;
	}


	public void setSexPieJson(String sexPieJson) {
		this.sexPieJson = sexPieJson;
	}


	public String getLineJson() {
		return lineJson;
	}


	public void setLineJson(String lineJson) {
		this.lineJson = lineJson;
	}


	public Map<String,String> getZoneMap() {
		return zoneMap;
	}


	public void setZoneMap(Map<String,String> zoneMap) {
		this.zoneMap = zoneMap;
	}
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
}
