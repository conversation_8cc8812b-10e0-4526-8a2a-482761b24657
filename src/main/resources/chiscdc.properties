#============================#
#===== Database sttings =====#
#============================#

#oracle database settings
jdbc.type=dm8
jdbc.driver=dm.jdbc.driver.DmDriver
#jdbc.url=*****************************************
#12c版本的配置jdbc.url=********************************************
jdbc.url=jdbc:dm://10.88.99.4:5236?compatibleMode=oracle
jdbc.username=SDZWCS
jdbc.password=Dameng123
jdbc.showSql=true
jdbc.testSql=SELECT 'x' FROM DUAL

#pool settings
jdbc.pool.init=1
jdbc.pool.minIdle=3
#数据库连接池最大连接数
jdbc.pool.maxActive=100

#========== activiti流程引擎配置=============#
#activiti font (windows font: 宋体  linux font: simsun)
activiti.diagram.activityFontName=simsun
activiti.diagram.labelFontName=simsun

#文件虚拟路径
virtual.directory=E:/webFile/

#不需要登录就能访问的文件路径（登录页面有小喇叭通知公告功能的平台需要配置：/files/notice/；登录页面有注册功能的平台则配置：/law/），多个用英文逗号隔开
publicFilePathList=/files/notice/

#缓存配置文件路径设置
ehcache.configFile=cache/ehcache-local.xml

#系统用户初始密码
#强密码规则：密码应为8-16位,必须包含大写字母、小写字母、数字和特殊字符，不能包含空格，不能包含连续3位顺序或逆序或重复的数字、字母!
initialPassword=Chiscdc@741

#连续登录错误次数
failTimes=5
#IP锁定时间
closeHours=6
#账号注册单位地区编码（“用人单位职业健康管理情况调查表”的用人单位地区下拉加载配置地区）
mainBusZoneGb = 3200000000
#账号注册审核菜单编码
fs_zhsh_menuEn=zczhsh


#短信发送信息（中卫信短信通道，涉及模块：专业技术人员抽取、在线考试）
#短信接口地址
send_url=http://************:7100/SmsService/SmsService?WSDL
#用户名
send_usename=cybercdc
#密码
send_psw=000000

#体检删除服务内网地址（体检录入、Word文书生成、国家数据导入等调用）
delUrl=http://***********:9600
#问卷调查、在线考试服务调用（可配置体检删除服务接口外网或专网地址）
delExtraNetUrl=http\://127.0.0.1\:8080
#体检录入撤销调用删除服务加密配置
encrypt.key=afmi5pjDeKpSUaLU
#为true不加密 注意 这个参数除了控制删除接口是否加密 还控制第三方请求返回的数据是否加密
encrypt.debug=true
#门户中通知数
headNoticeNum=10
#门户头部快捷菜单最多显示个数
headMenuNum=6

#登录相关配置
#登录是否验证短信验证码，1验证0不验证
login.ifSendSms=0
#1天允许发送短信的次数
login.sendSmsTimes=20
#短信验证码有效期，默认15分钟
login.smsValidMin=15
#短信模板 武汉、江苏使用（武汉需要前边或者末尾存在【XXX】否则短信接口校验失败）
#江苏：验证码：checkCode，用于江苏省职业病防治信息管理平台登录，如非本人操作，请忽略此信息，有效期：smsValidMin分钟。
#武汉：【武汉疾控】验证码：checkCode，用于武汉市职业病防治综合管理信息平台登录，如非本人操作，请忽略此信息，有效期：smsValidMin分钟。
login.smsTemplate=验证码：checkCode，用于江苏省职业病防治信息管理平台登录，如非本人操作，请忽略此信息，有效期：smsValidMin分钟。
#武汉:用于找回密码短信发送模板
login.passwordSmsTemplate=【武汉疾控】验证码：checkCode，用于武汉市职业病防治综合管理信息平台忘记账号密码，如非本人操作，请忽略此信息，有效期：smsValidMin分钟。
#登录短信接口相关配置
#江苏省疾控中心特有短信测试地址:http://36.137.215.180:8081/webserviceMas/services/cmcc_mas_wbs?wsdl
#武汉疾控短信地址 http://39.107.226.15:8088/v2sms.aspx
login.sendSms.url=http://************:7100/SmsService/SmsService?WSDL
#江苏短信接口调用 特有参数
login.sendSms.ApplicationID=p202109031244443
#武汉短信接口调用 特有参数 用户Id
login.smsUserId=163
#武汉短信接口调用 特有参数 用户账号
login.smsUserName=zybfzy
#武汉短信接口调用 特有参数 密码
login.smsPassWord=123456

#用于新疆职卫与现状调查系统单点登录对接加密秘钥，根据encrypt.debug参数是否启用加密模式
encrypt.thirdpart.key=1bc5D6HdeSYmjsZU

#【注册账号审核】注册账号有效期（年）（为空时，默认显示空；不为空时，默认显示“有效开始日期+参数年份”的日期）（武汉配置：5）
account.defaultValidYear=

#解决csrf跨站请求伪造漏洞，如第三方系统需要单点登录至我们的系统，则需要配置在白名单里面才能跳转，支持ip+端口及域名的方式
#如果平台服务使用Nginx部署，需要在这里配置Nginx服务的IP或者域名，多个用英文逗号隔开
whiteList=

#数据库查询默认最大结果数限制，0或空表示不限制
db.query.maxResults=0
