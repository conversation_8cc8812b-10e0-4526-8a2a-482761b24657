<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/heth/statisticsTemplate.xhtml">

    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{tbTjBhkNewBean}" />

    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <script type="text/javascript">
            //<![CDATA[
            window.onload = function () {
            	buildPicture();
            };
            var ecConfig = echarts.config;
			var myChart ;
			var lineleftChart;
			var linerightChart;
            var macarons = theme();
            function buildPicture() {
            	if (myChart != null) {
		    		myChart.clear();
				}	
		    	var badRsnDiv = document.getElementById("chartImage");
				if (badRsnDiv != null) {
					myChart = echarts.init(badRsnDiv, 'macarons');
					 
				    var json = document.getElementById("mainForm:chartJson").value;
					if (json != '') {
						myChart.setOption(eval("(" + json + ")"));
					}
				}
				buildLeftLineChart();
				buildRightLineChart();
            }
            function buildLeftLineChart() {
				if (lineleftChart != null) {
					lineleftChart.clear();
				}
				var lineLeftDiv = document.getElementById("lineLeftDiv");
				if (lineLeftDiv != null) {
					var lineleftChart = echarts.init(lineLeftDiv, 'macarons');
					 
				    var json = document.getElementById("mainForm:lineLeftJson").value;
					if (json != '') {
						lineleftChart.setOption(eval("(" + json + ")"));
					}
				}
			}
            function buildRightLineChart() {
				if (lineRightChart != null) {
					lineRightChart.clear();
				}
				var lineRightDiv = document.getElementById("lineRightDiv");
				if (lineRightDiv != null) {
					var lineRightChart = echarts.init(lineRightDiv, 'macarons');
					 
				    var json = document.getElementById("mainForm:lineRightJson").value;
					if (json != '') {
						lineRightChart.setOption(eval("(" + json + ")"));
					}
				}
			}
            
            function showStatus() {
				PF('StatusDialog').show();
			}
			function hideStatus() {
				PF('StatusDialog').hide();
			}
            //]]>
        </script>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertCharTitle">
        <p:row>
            <p:column colspan="6"
                      style="text-align:left;padding-left:5px;height: 20px;">
                <p:outputLabel value="职业健康检查质量分析"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertCharButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="2"
                         style="border-color:transparent;padding:0;">
				<span class="ui-separator"><span
                        class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="分析" icon="ui-icon-search" onclick="showStatus()"  oncomplete="hideStatus()" action="#{mgrbean.searchAction}" process="@this,mainGrid,zone"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertCharSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:8%;">
				<p:outputLabel value="地区：" />
			</p:column>
			<p:column style="text-align:left;padding-left:3px;width: 15%;">
				<zwx:ZoneSingleComp zoneList="#{mgrbean.zoneList}" id="zone"
					zoneCode="#{mgrbean.searchZoneCode}"
					zoneName="#{mgrbean.searchZoneName}"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:8%;">
				<p:outputLabel value="查询日期：" />
			</p:column>
			<p:column style="text-align:left;padding-left:8px;width: 16%;">
				<p:calendar pattern="yyyy-MM-dd" maxlength="10" showOtherMonths="true" id="searchStartDate"
                               navigator="true" yearRange="c-10:c+10"  size="11" showButtonPanel="true"
                               converterMessage="诊断日期，格式输入不正确！" maxdate="new Date()"
                               value="#{mgrbean.searchStartDate}"/>
                   <p:outputLabel value="～"/>
                   <p:calendar pattern="yyyy-MM-dd" maxlength="10" showOtherMonths="true" id="searchEndDate"
                               navigator="true" yearRange="c-10:c+10"  size="11" showButtonPanel="true"
                               converterMessage="诊断日期，格式输入不正确！" maxdate="new Date()"
                               value="#{mgrbean.searchEndDate}"/>
			</p:column>
			<p:column style="text-align:right;padding-right:3px;width:8%">
                <h:outputText value="分析类型：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:selectOneRadio id="searchType" value="#{mgrbean.searchType}" style="width: 180px;">
                    <f:selectItem itemLabel="按机构" itemValue="0"/>
                    <f:selectItem itemLabel="按地区" itemValue="1"/>
                </p:selectOneRadio>
            </p:column>
        </p:row>
        <p:row>
        	<p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="职业危害因素：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="5">
                <h:panelGrid columns="5" 
					style="border-color: #ffffff;margin: 0px;padding: 0px;">
					<p:inputText id="searchBadrsns" value="#{mgrbean.searchBadrsns}"  style="width:280px;" readonly="true"/>
					<p:commandLink styleClass="ui-icon ui-icon-search"
						id="initTreeLink" process="@this"
						style="position: relative;left: -30px;top:0px;"
						oncomplete="PF('OveralPanel').show()" />
					<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
						style="position: relative;left: -28px;"
						action="#{mgrbean.clearRsnAction}" process="@this"
						update="searchBadrsns,overalPanel"/>
				</h:panelGrid>
				<p:overlayPanel id="overalPanel" for="searchBadrsns"
					dynamic="false" style="width:280px;" widgetVar="OveralPanel"
					showCloseIcon="true">
					<p:tree value="#{mgrbean.sortTree}" var="node"
						selectionMode="single" id="choiceTree"
						style="width: 250px;height: 300px;overflow-y: auto;">
						<p:ajax event="select" 
							process="@this" 
							listener="#{mgrbean.onsortNodeSelect}"/>
						<p:treeNode>
							<h:outputText value="#{node.codeName}" />
						</p:treeNode>
					</p:tree>
				</p:overlayPanel>
			</p:column>	
        </p:row>
    </ui:define>

    <ui:define name="insertChart">
        <p:outputPanel id="overviewPanel">
        	<h:inputHidden id="chartJson" value="#{mgrbean.chartJson}"></h:inputHidden>
			<h:inputHidden id="lineLeftJson" value="#{mgrbean.lineLeftJson}"></h:inputHidden>
			<h:inputHidden id="lineRightJson" value="#{mgrbean.lineRightJson}"></h:inputHidden>
            <div id="chartImage" style="width:100%;height:500px;"></div>
            <div style="width:100%;height:20px;border-bottom:1px solid #d4d4d4"></div>
			<div style="width:100%;height:450px;display:flex;padding-top:10px" id="otherDiv">
				<div id="lineLeftDiv" style="width:48%;height:100%;"></div>
				<div id="middleDiv" style="width:4%;height:450px;border-left:1px solid #d4d4d4"></div>
				<div id="lineRightDiv" style="width:48%;height:100%;"></div>
			</div>
        </p:outputPanel>
    </ui:define>

    <ui:define name="insertCharDataTable">
    	<p:outputPanel id="dataView">
			<p:panelGrid style="width:99.5%;margin-bottom:5px;" styleClass="#{mgrbean.searchType==0?'globleTable_removeFirstCol':'globleTable'}">
        	   <f:facet name="header">	
        		<p:row>
        			<p:column  styleClass="ui-state-default" style="text-align: center;width:150px;">
				    	<h:outputText value="地区"></h:outputText>
				    </p:column>
				    <p:column  styleClass="ui-state-default" 
				    	style="text-align: center;width:240px;"
				    	rendered="#{mgrbean.searchType==0}">
				    	<h:outputText value="机构简称"></h:outputText>
				    </p:column>
				    <p:column  styleClass="ui-state-default" style="text-align: center;width:150px;">
				    	<h:outputText value="总人次数"></h:outputText>
				    </p:column>
				     <p:column  styleClass="ui-state-default" style="text-align: center;width:150px;">
				    	<h:outputText value="复检总人次数"></h:outputText>
				    </p:column>
				    <p:column  styleClass="ui-state-default" style="text-align: center;width:150px;">
				    	<h:outputText value="总人数"></h:outputText>
				    </p:column>
				     <p:column  styleClass="ui-state-default" style="text-align: center;width:150px;">
				    	<h:outputText value="必检项目符合人次数"></h:outputText>
				    </p:column>
				     <p:column  styleClass="ui-state-default" style="text-align: center;width:150px;">
				    	<h:outputText value="规范率（%）"></h:outputText>
				    </p:column>
				     <p:column  styleClass="ui-state-default" style="text-align: center;width:150px;" rendered="#{mgrbean.ifShowCheckStdRate}">
				    	<h:outputText value="审核后符合人次数"></h:outputText>
				    </p:column>
				     <p:column  styleClass="ui-state-default" style="text-align: center;width:150px;" rendered="#{mgrbean.ifShowCheckStdRate}">
				    	<h:outputText value="审核后规范率（%）"></h:outputText>
				    </p:column>
				     <p:column  styleClass="ui-state-default" style="text-align: center;width:150px;">
				    	<h:outputText value="职业禁忌证检出数"></h:outputText>
				    </p:column>
				     <p:column  styleClass="ui-state-default" style="text-align: center;width:150px;">
				    	<h:outputText value="职业禁忌证检出率（%）"></h:outputText>
				    </p:column>
				     <p:column  styleClass="ui-state-default" style="text-align: center;width:150px;">
				    	<h:outputText value="疑似职业病检出数"></h:outputText>
				    </p:column>
				     <p:column  styleClass="ui-state-default" style="text-align: center;width:150px;">
				    	<h:outputText value="疑似职业病检出率（%）"></h:outputText>
				    </p:column>
        		</p:row>
        	</f:facet>	
        		<c:forEach items="#{mgrbean.dataList}" var="data" varStatus="row">
					 <p:row>
					 	<c:if test="#{row.index!=mgrbean.dataList.size()-1}">
					 		<p:column style="text-align: center;line-height: 24px;display:#{((data[12] != null and (data[12] == 0 or data[12] == '0')) and mgrbean.searchType==0) ?'none':''}" rowspan="#{data[12] != null and data[12] != '' and data[12] != 's' ? data[12] : 1}" rendered="#{mgrbean.searchType==0 and data[12] != null and data[12] != '' and data[12] != 's' and data[12] != '0' and data[12] != 0}">
						    	<h:outputText value="#{data[0]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;display:#{mgrbean.searchType==1?'':'none'}">
						    	<h:outputText value="#{data[0]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: left;line-height: 24px; display:#{mgrbean.searchType==0?'':'none'}" >
						    	<h:outputText value="#{data[1]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[2]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[3]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[13]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[4]}"></h:outputText>
						    </p:column>
						    
						    <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[5]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;" rendered="#{mgrbean.ifShowCheckStdRate}">
						    	<h:outputText value="#{data[6]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;" rendered="#{mgrbean.ifShowCheckStdRate}">
						    	<h:outputText value="#{data[7]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[8]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[9]}"></h:outputText>
						    </p:column>
						     <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[10]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[11]}"></h:outputText>
						    </p:column>
					 	 </c:if>
					 	 <c:if test="#{row.index==mgrbean.dataList.size()-1}">
						    <p:column style="text-align: right;line-height: 24px;" colspan="#{mgrbean.searchType==0?'2':'1'}">
						    	<h:outputText value="#{data[0]}" style="font-weight:bold;"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[2]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[3]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[13]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[4]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[5]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;"  rendered="#{mgrbean.ifShowCheckStdRate}">
						    	<h:outputText value="#{data[6]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;"  rendered="#{mgrbean.ifShowCheckStdRate}">
						    	<h:outputText value="#{data[7]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[8]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[9]}"></h:outputText>
						    </p:column>
						     <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[10]}"></h:outputText>
						    </p:column>
						    <p:column style="text-align: center;line-height: 24px;">
						    	<h:outputText value="#{data[11]}"></h:outputText>
						    </p:column>
					 	 </c:if>
					 	</p:row> 
					 </c:forEach>	
					 <p:row rendered="#{null==mgrbean.dataList or mgrbean.dataList.size()==0}">
					    <p:column colspan="#{mgrbean.searchType==0?'13':'11'}">
					    	<h:outputText value="未查询到数据！"></h:outputText>
					    </p:column>
					</p:row>
        	</p:panelGrid>			
		</p:outputPanel>	
    </ui:define>
</ui:composition>